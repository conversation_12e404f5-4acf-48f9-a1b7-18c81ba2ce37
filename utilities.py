import bpy
import openai
import re
import os
import sys
import requests
import json
import hashlib
import base64
import ast


def hash_api_key(api_key):
    """Hash API key for secure storage"""
    if not api_key:
        return ""
    salt = os.urandom(32)
    key = hashlib.pbkdf2_hmac(
        'sha256',
        api_key.encode('utf-8'),
        salt,
        100000
    )
    return base64.b64encode(salt + key).decode('ascii')

def verify_api_key(hashed_key, api_key):
    """Verify API key against stored hash"""
    if not hashed_key or not api_key:
        return False
    decoded = base64.b64decode(hashed_key)
    salt = decoded[:32]
    stored_key = decoded[32:]
    new_key = hashlib.pbkdf2_hmac(
        'sha256',
        api_key.encode('utf-8'),
        salt,
        100000
    )
    return stored_key == new_key

def get_api_key(context, addon_name):
    preferences = context.preferences
    addon_prefs = preferences.addons[addon_name].preferences
    # Return raw API key (temporary - will be updated to use secure storage)
    return addon_prefs.api_key


def init_props():
    bpy.types.Scene.gpt4_chat_history = bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)
    bpy.types.Scene.gpt4_model = bpy.props.EnumProperty(
    name="GPT Model",
    description="Select the GPT model to use",
    items=[
        ("gpt-4", "GPT-4 (powerful, expensive)", "Use GPT-4"),
        ("gpt-3.5-turbo", "GPT-3.5 Turbo (less powerful, cheaper)", "Use GPT-3.5 Turbo"),
    ],
    default="gpt-4",
)
    bpy.types.Scene.gpt4_chat_input = bpy.props.StringProperty(
        name="Message",
        description="Enter your message",
        default="",
    )
    bpy.types.Scene.gpt4_button_pressed = bpy.props.BoolProperty(default=False)
    bpy.types.PropertyGroup.type = bpy.props.StringProperty()
    bpy.types.PropertyGroup.content = bpy.props.StringProperty()

def clear_props():
    del bpy.types.Scene.gpt4_chat_history
    del bpy.types.Scene.gpt4_chat_input
    del bpy.types.Scene.gpt4_button_pressed

import requests

def generate_blender_code(prompt, chat_history, context, system_prompt, provider_config):
    messages = [{"role": "system", "content": system_prompt}]
    for message in chat_history[-10:]:
        if message.type == "assistant":
            messages.append({"role": "assistant", "content": "```\n" + message.content + "\n```"})
        else:
            messages.append({"role": message.type.lower(), "content": message.content})

    # Add the current user message
    messages.append({"role": "user", "content": "Can you please write Blender code for me that accomplishes the following task: " + prompt + "? \n. Do not respond with anything that is not Python code. Do not provide explanations"})

    try:
        # Verify API key before using
        if not provider_config.api_key:
            raise ValueError("API key cannot be empty")
        if not provider_config.verify_key(provider_config.api_key):
            raise ValueError("Invalid API key - verification failed")
            
        if provider_config.type == 'openai':
            # OpenAI API v1.x implementation
            client = openai.OpenAI(
                api_key=provider_config.api_key,
                base_url=provider_config.base_url if provider_config.base_url else None
            )
            
            response = client.chat.completions.create(
                model=context.scene.gpt4_model,
                messages=messages,
                stream=True,
                max_tokens=1500,
            )

            completion_text = ''
            for chunk in response:
                if chunk.choices[0].delta.content:
                    event_text = chunk.choices[0].delta.content
                    completion_text += event_text
                    print(completion_text, flush=True, end='\r')

            # Clear sensitive data from memory
            del client
            provider_config.api_key = ""
            del response

        else:  # OpenAI compatible API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {provider_config.api_key}"
            }
            proxies = {'http': provider_config.proxy_url, 'https': provider_config.proxy_url} if provider_config.proxy_url else None
            
            payload = {
                "model": context.scene.gpt4_model,
                "messages": messages,
                "stream": True,
                "max_tokens": 1500
            }
            
            response = requests.post(
                f"{provider_config.base_url}/chat/completions",
                headers=headers,
                json=payload,
                stream=True,
                proxies=proxies
            )
            response.raise_for_status()
            
            completion_text = ''
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data:'):
                        data = decoded_line[5:].strip()
                        if data == '[DONE]':
                            break
                        try:
                            event = json.loads(data)
                            if 'choices' in event and len(event['choices']) > 0:
                                if 'delta' in event['choices'][0] and 'content' in event['choices'][0]['delta']:
                                    event_text = event['choices'][0]['delta']['content']
                                    completion_text += event_text
                                    print(completion_text, flush=True, end='\r')
                        except json.JSONDecodeError:
                            continue

        # Extract code block from completion text
        code_blocks = re.findall(r'```(.*?)```', completion_text, re.DOTALL)
        if not code_blocks:
            return None
            
        completion_text = code_blocks[0]
        completion_text = re.sub(r'^python', '', completion_text, flags=re.MULTILINE)
        return completion_text.strip()

    except Exception as e:
        print(f"Error in generate_blender_code: {str(e)}")
        # Clear sensitive data from memory
        if 'provider_config' in locals():
            provider_config.api_key = ""
        return None

RISKY_KEYWORDS = {
    # Dosya sistemi işlemleri
    'os.remove', 'os.unlink', 'os.rmdir', 'os.makedirs', 'os.removedirs', 'os.rename', 'os.renames',
    'shutil.rmtree', 'shutil.move', 'shutil.copy', 'shutil.copy2', 'shutil.copyfile', 'shutil.copymode',
    'shutil.copystat', 'shutil.copytree',
    'open', # Dosya açma genel olarak riskli olabilir, moduna bakmak gerekir ama basitlik için ekleyelim

    # Ağ işlemleri
    'socket.socket', 'requests.get', 'requests.post', 'requests.put', 'requests.delete', 'urllib.request.urlopen',

    # Kod çalıştırma / Değerlendirme
    'eval', 'exec', 'subprocess.call', 'subprocess.run', 'subprocess.Popen', 'os.system',

    # Blender veri silme (potansiyel olarak)
    'bpy.ops.object.delete', 'bpy.ops.mesh.delete', 'bpy.data.objects.remove', 'bpy.data.meshes.remove',
    'bpy.data.materials.remove', 'bpy.data.textures.remove', 'bpy.data.images.remove',
    'bpy.data.collections.remove',
    # TODO: Daha fazla Blender'a özgü riskli operasyon eklenebilir
}

# İzin verilen modüller (bunların içindeki her şey riskli sayılmaz, sadece yukarıdaki keyword'ler aranır)
# Ancak, `os` gibi bir modülün kendisi import edildiğinde, içindeki fonksiyonlar ayrıca kontrol edilir.
# Bu liste daha çok `bpy.ops.object.delete` gibi tam yolları yakalamak için.
# Şimdilik RISKY_KEYWORDS daha doğrudan olduğu için bunu kullanmayabiliriz veya farklı bir mantıkla entegre edebiliriz.

def analyze_code_risk(code_string):
    """
    Verilen Python kodunu analiz eder ve potansiyel riskleri belirler.
    Riskli anahtar kelimeler, fonksiyonlar veya modüller içerip içermediğini kontrol eder.

    Args:
        code_string (str): Analiz edilecek Python kodu.

    Returns:
        dict: {
            "status": "Güvenli" | "Riskli",
            "risky_lines": set(int) # Riskli bulunan satır numaraları
            "details": list(str) # Riskli bulunan anahtar kelimeler/nedenler
        }
    """
    risky_lines = set()
    risk_details = []
    is_risky = False

    try:
        tree = ast.parse(code_string)
        
        for node in ast.walk(tree):
            # Fonksiyon çağrılarını kontrol et (örn: os.remove(), bpy.ops.object.delete())
            if isinstance(node, ast.Call):
                func_name = ''
                if isinstance(node.func, ast.Attribute): # obj.method()
                    # obj.attr1.attr2.method() gibi uzun zincirleri yakalamak için
                    attr_chain = []
                    curr = node.func
                    while isinstance(curr, ast.Attribute):
                        attr_chain.append(curr.attr)
                        curr = curr.value
                    if isinstance(curr, ast.Name): # En baştaki obje (örn: os, bpy)
                        attr_chain.append(curr.id)
                        func_name = ".".join(reversed(attr_chain))
                    else: # Başka bir şeyse (örn: self.method()), şimdilik basit tutalım
                        func_name = node.func.attr
                elif isinstance(node.func, ast.Name): # function()
                    func_name = node.func.id
                
                if func_name in RISKY_KEYWORDS:
                    is_risky = True
                    risky_lines.add(node.lineno)
                    risk_details.append(f"Satır {node.lineno}: Riskli fonksiyon çağrısı '{func_name}'")

            # Import'ları kontrol et (örn: import os, from os import remove)
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    if alias.name in RISKY_KEYWORDS or f"{alias.name}." in "".join(RISKY_KEYWORDS) : #örn: os modülü
                        # Eğer modülün kendisi riskliyse (örn: 'os' gibi bir keyword listemizde yok ama 'os.remove' var)
                        # Bu, modülün içindeki her fonksiyonun riskli olduğu anlamına gelmez,
                        # ama modülün import edilmesi bir uyarı olabilir.
                        # Şimdilik, doğrudan keyword eşleşmesine odaklanalım.
                        # Eğer 'os' gibi genel bir modül adı RISKY_KEYWORDS'de olsaydı, burası tetiklenirdi.
                        pass # Fonksiyon çağrıları zaten yukarıda yakalanıyor.
            elif isinstance(node, ast.ImportFrom):
                module_name = node.module
                if module_name: # from . import x durumunda module None olabilir
                    for alias in node.names:
                        imported_as = f"{module_name}.{alias.name}"
                        if imported_as in RISKY_KEYWORDS:
                            is_risky = True
                            risky_lines.add(node.lineno)
                            risk_details.append(f"Satır {node.lineno}: Riskli import '{imported_as}'")
                        elif alias.name in RISKY_KEYWORDS: #örn: from some_module import eval
                            is_risky = True
                            risky_lines.add(node.lineno)
                            risk_details.append(f"Satır {node.lineno}: Riskli import '{alias.name}' from '{module_name}'")
            
            # exec() ve eval() gibi özel durumlar (ast.Call içinde de yakalanabilir ama burada daha net)
            # ast.Call içinde zaten 'eval' ve 'exec' yakalanıyor.

    except SyntaxError as e:
        return {
            "status": "Hatalı Kod",
            "risky_lines": set(),
            "details": [f"Kod sözdizimi hatası: {e}"]
        }

    return {
        "status": "Riskli" if is_risky else "Güvenli",
        "risky_lines": risky_lines,
        "details": risk_details
    }


def split_area_to_text_editor(context):
    area = context.area
    for region in area.regions:
        if region.type == 'WINDOW':
            override = {'area': area, 'region': region}
            bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
            break

    new_area = context.screen.areas[-1]
    new_area.type = 'TEXT_EDITOR'
    return new_area
