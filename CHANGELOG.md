# BlenderGPT Change Log

## [2.0.0] - 2025-06-05

### Fixed
- Kritik yazım hatası: `unegister_class` → `unregister_class` <PERSON><PERSON><PERSON><PERSON><PERSON> (#283 satır)

### Security
- `exec()` fonksiyonu gü<PERSON> hale getirildi (sınırlı namespace)
- API anahtarları için PBKDF2-HMAC-SHA256 hash mekanizması eklendi
- Bellek temizleme mekanizmaları implemente edildi
- Has<PERSON>s veriler kullanım sonrası bellekten temizleniyor

### Changed
- OpenAI API v0.x → v1.x'e yükseltildi
  - `openai.ChatCompletion.create` → `client.chat.completions.create`
  - Yeni response formatına uyum sağlandı
- Kod yapısı iyileştirildi ve tekrarlar kaldırıldı

### Added
- API anahtarı doğrulama sistemi eklendi
- Detaylı hata yönetimi eklendi
- Güvenlik logları eklendi
