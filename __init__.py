import sys
import os
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

import openai

from .utilities import *
bl_info = {
    "name": "GPT-4 Blender Assistant",
    "blender": (2, 82, 0),
    "category": "Object",
    "author": "<PERSON><PERSON><PERSON> (@gd3kr)",
    "version": (2, 0, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Generate Blender Python code using OpenAI's GPT-4 to perform various tasks.",
    "warning": "",
    "wiki_url": "",
    "tracker_url": "",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown (```). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```"""



class GPT4_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "gpt4.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.gpt4_chat_history.remove(self.message_index)
        return {'FINISHED'}

class GPT4_OT_ShowCode(bpy.types.Operator):
    bl_idname = "gpt4.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "GPT4_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)
        
        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class GPT4_PT_Panel(bpy.types.Panel):
    bl_label = "GPT-4 Blender Assistant"
    bl_idname = "GPT4_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'GPT-4 Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        column.label(text="Chat history:")
        box = column.box()
        for index, message in enumerate(context.scene.gpt4_chat_history):
            if message.type == 'assistant':
                row = box.row()
                row.label(text="Assistant: ")
                show_code_op = row.operator("gpt4.show_code", text="Show Code")
                show_code_op.code = message.content
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                row = box.row()
                row.label(text=f"User: {message.content}")
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()
        
        column.label(text="GPT Model:")
        column.prop(context.scene, "gpt4_model", text="")

        column.label(text="Enter your message:")
        column.prop(context.scene, "gpt4_chat_input", text="")
        button_label = "Please wait...(this might take some time)" if context.scene.gpt4_button_pressed else "Execute"
        row = column.row(align=True)
        row.operator("gpt4.send_message", text=button_label)
        row.operator("gpt4.clear_chat", text="Clear Chat")

        column.separator()

class GPT4_OT_ClearChat(bpy.types.Operator):
    bl_idname = "gpt4.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.gpt4_chat_history.clear()
        return {'FINISHED'}

class GPT4_OT_Execute(bpy.types.Operator):
    bl_idname = "gpt4.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    def execute(self, context):
        prefs = context.preferences.addons[__name__].preferences
        
        # Check if there are any providers configured
        if len(prefs.providers) == 0:
            self.report({'ERROR'}, "No AI providers configured. Please add one in addon preferences.")
            return {'CANCELLED'}

        # Get active provider
        provider = prefs.providers[prefs.active_provider_index]

        # Validate provider configuration
        if not provider.api_key:
            self.report({'ERROR'}, f"No API key configured for provider: {provider.name}")
            return {'CANCELLED'}

        context.scene.gpt4_button_pressed = True
        bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)
        
        try:
            blender_code = generate_blender_code(
                prompt=context.scene.gpt4_chat_input,
                chat_history=context.scene.gpt4_chat_history,
                context=context,
                system_prompt=system_prompt,
                provider_config=provider
            )

            message = context.scene.gpt4_chat_history.add()
            message.type = 'user'
            message.content = context.scene.gpt4_chat_input

            # Clear the chat input field
            context.scene.gpt4_chat_input = ""

            if blender_code:
                message = context.scene.gpt4_chat_history.add()
                message.type = 'assistant'
                message.content = blender_code

                # Create a safe namespace with only allowed modules
                safe_globals = {
                    '__builtins__': {
                        'range': range,
                        'len': len,
                        'str': str,
                        'int': int,
                        'float': float,
                        'list': list,
                        'dict': dict,
                        'tuple': tuple,
                        'bool': bool,
                        'enumerate': enumerate,
                        'zip': zip,
                        'min': min,
                        'max': max,
                        'sum': sum,
                        'abs': abs,
                        'round': round
                    },
                    'bpy': bpy,
                    'math': __import__('math'),
                    'random': __import__('random')
                }
                safe_locals = {}
                exec(blender_code, safe_globals, safe_locals)

        except Exception as e:
            self.report({'ERROR'}, f"Error during code generation/execution: {str(e)}")
            return {'CANCELLED'}
        finally:
            context.scene.gpt4_button_pressed = False

        return {'FINISHED'}


def menu_func(self, context):
    self.layout.operator(GPT4_OT_Execute.bl_idname)

class ProviderConfig(bpy.types.PropertyGroup):
    name: bpy.props.StringProperty(name="Provider Name", default="New Provider")
    type: bpy.props.EnumProperty(
        name="Provider Type",
        items=[
            ('openai', 'OpenAI', 'Official OpenAI API'),
            ('openai_compatible', 'OpenAI Compatible', 'Any OpenAI compatible API')
        ],
        default='openai'
    )
    base_url: bpy.props.StringProperty(
        name="API Base URL",
        default="https://api.openai.com/v1",
        description="API endpoint URL (e.g. https://api.openai.com/v1)"
    )
    _api_key_hash: bpy.props.StringProperty(
        name="API Key Hash",
        default="",
        options={'HIDDEN'}
    )
    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your API Key",
        default="",
        subtype="PASSWORD",
        update=lambda self, context: setattr(self, '_api_key_hash', hash_api_key(self.api_key))
    )
    proxy_url: bpy.props.StringProperty(
        name="Proxy URL",
        description="Optional proxy URL (e.g. http://proxy.example.com:8080)",
        default=""
    )

    def verify_key(self, key_to_check):
        """Verify if the provided key matches the stored hash"""
        from .utilities import verify_api_key
        return verify_api_key(self._api_key_hash, key_to_check)

class GPT4_UL_ProvidersList(bpy.types.UIList):
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            layout.label(text=item.name, icon='SERVER')
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon='SERVER')

class GPT4_OT_AddProvider(bpy.types.Operator):
    bl_idname = "gpt4.add_provider"
    bl_label = "Add Provider"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        prefs = context.preferences.addons[__name__].preferences
        provider = prefs.providers.add()
        provider.name = f"Provider {len(prefs.providers)}"
        prefs.active_provider_index = len(prefs.providers) - 1
        return {'FINISHED'}

class GPT4_OT_RemoveProvider(bpy.types.Operator):
    bl_idname = "gpt4.remove_provider"
    bl_label = "Remove Provider"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        prefs = context.preferences.addons[__name__].preferences
        if len(prefs.providers) > 0:
            prefs.providers.remove(prefs.active_provider_index)
            if prefs.active_provider_index > 0:
                prefs.active_provider_index -= 1
        return {'FINISHED'}

class GPT4_OT_TestProvider(bpy.types.Operator):
    bl_idname = "gpt4.test_provider"
    bl_label = "Test Provider Connection"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        prefs = context.preferences.addons[__name__].preferences
        provider = prefs.providers[prefs.active_provider_index]
        
        try:
            # Test connection with a simple request
            test_prompt = [{"role": "user", "content": "Say 'test'"}]
            response = generate_blender_code(
                prompt="test",
                chat_history=[],
                context=context,
                system_prompt="",
                provider_config=provider
            )
            
            if response:
                self.report({'INFO'}, "Connection successful!")
            else:
                self.report({'ERROR'}, "Empty response from provider")
        except Exception as e:
            self.report({'ERROR'}, f"Connection failed: {str(e)}")
        
        return {'FINISHED'}

class GPT4AddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    providers: bpy.props.CollectionProperty(type=ProviderConfig)
    active_provider_index: bpy.props.IntProperty(default=0)

    def draw(self, context):
        layout = self.layout
        
        # Providers list
        row = layout.row()
        row.template_list(
            "GPT4_UL_ProvidersList", "",
            self, "providers",
            self, "active_provider_index",
            rows=3
        )
        
        # Provider management buttons
        col = row.column(align=True)
        col.operator("gpt4.add_provider", icon='ADD', text="")
        col.operator("gpt4.remove_provider", icon='REMOVE', text="")
        
        # Active provider settings
        if len(self.providers) > 0:
            provider = self.providers[self.active_provider_index]
            box = layout.box()
            box.label(text="Provider Settings", icon='PREFERENCES')
            
            box.prop(provider, "name")
            box.prop(provider, "type")
            box.prop(provider, "base_url")
            box.prop(provider, "api_key")
            box.prop(provider, "proxy_url")
            
            # Test connection button
            box.operator("gpt4.test_provider", text="Test Connection")

def register():
    # Register property classes first
    bpy.utils.register_class(ProviderConfig)
    
    # Register UI and operator classes
    bpy.utils.register_class(GPT4_UL_ProvidersList)
    bpy.utils.register_class(GPT4_OT_AddProvider)
    bpy.utils.register_class(GPT4_OT_RemoveProvider)
    bpy.utils.register_class(GPT4_OT_TestProvider)
    bpy.utils.register_class(GPT4AddonPreferences)
    bpy.utils.register_class(GPT4_OT_Execute)
    bpy.utils.register_class(GPT4_PT_Panel)
    bpy.utils.register_class(GPT4_OT_ClearChat)
    bpy.utils.register_class(GPT4_OT_ShowCode)
    bpy.utils.register_class(GPT4_OT_DeleteMessage)

    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()


def unregister():
    bpy.utils.unregister_class(GPT4AddonPreferences)
    bpy.utils.unregister_class(GPT4_OT_Execute)
    bpy.utils.unregister_class(GPT4_PT_Panel)
    bpy.utils.unregister_class(GPT4_OT_ClearChat)
    bpy.utils.unregister_class(GPT4_OT_ShowCode)
    bpy.utils.unregister_class(GPT4_OT_DeleteMessage)

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
