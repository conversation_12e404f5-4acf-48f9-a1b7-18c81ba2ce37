import bpy
import unittest
import os
import sys

# Addon'un kök dizinini sys.path'e ekle
# Bu, testlerin eklenti modüllerini bulmasını sağlar
addon_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if addon_root not in sys.path:
    sys.path.append(addon_root)

# Eklentiyi etkinleştirmeyi dene (test ortamında gerekli olabilir)
# B<PERSON> kısım, testlerin nasıl çalıştırıldığına bağlı olarak ayarlanabilir
try:
    # Eklentinin kayıtlı adını bulmak için bl_info'yu kullanabiliriz
    # Ancak __init__.py'yi doğrudan import etmek döngüsel bağımlılıklara yol açabilir
    # Bu yüzden eklentinin adını manuel olarak belirtmek daha güvenli olabilir
    # veya eklentinin zaten etkin olduğunu varsayabiliriz.
    # <PERSON><PERSON><PERSON><PERSON>, eklentinin adının 'BlenderGPT' olduğunu varsayalım.
    # Gerçek dosya adı __init__.py ise, Blender'daki modül adı genellikle klasör adı olur.
    # Proje yapısına göre bu 'BlenderGPT-main' veya sadece 'BlenderGPT' olabilir.
    # Dosya yapısına bakarak, eklenti klasörünün adı 'BlenderGPT-main' gibi görünüyor.
    # Ancak Blender eklentileri genellikle __init__.py dosyasının bulunduğu klasörün adıyla anılır.
    # Eğer __init__.py doğrudan BlenderGPT-main içindeyse, modül adı 'BlenderGPT-main' olur.
    # Eğer bir alt klasördeyse (örn: BlenderGPT-main/BlenderGPT/__init__.py), o zaman 'BlenderGPT' olur.
    # Mevcut durumda __init__.py ana dizinde, bu yüzden eklenti adı muhtemelen dosya adıdır.
    # Blender eklentileri için modül adı genellikle __init__.py dosyasının bulunduğu klasörün adıdır.
    # Bu durumda, klasör adı "BlenderGPT-main" ise, modül adı da o olmalıdır.
    module_name = os.path.basename(addon_root) # Bu genellikle doğru eklenti adını verir
    if not bpy.context.preferences.addons.get(module_name):
        bpy.ops.preferences.addon_enable(module=module_name)
    if not bpy.context.preferences.addons.get(module_name).preferences:
         # Eğer tercihler yüklenmediyse, bu bir sorun olabilir.
         # Testlerin eklenti tam olarak yüklendikten sonra çalışması gerekir.
         print(f"Uyarı: {module_name} eklentisinin tercihleri yüklenemedi.")

except Exception as e:
    print(f"Eklenti etkinleştirilemedi veya zaten etkin: {e}")
    print(f"Kullanılan modül adı: {module_name}")
    print("Lütfen testleri çalıştırmadan önce eklentinin Blender'da manuel olarak etkinleştirildiğinden emin olun.")


# __init__ içindeki operatörü import etmeden önce eklentinin yüklendiğinden emin olalım.
# Bu, yukarıdaki enable bloğundan sonra gelmeli.
try:
    from __init__ import GPT4_OT_Execute
except ImportError as e:
    print(f"ImportError: {e}. Eklenti modülleri bulunamadı. sys.path kontrol edin: {sys.path}")
    # sys.exit(1) # Testleri durdurabiliriz, çünkü operatör olmadan devam edemeyiz.
    # Şimdilik bir uyarı verip devam edelim, belki kullanıcı manuel olarak ayarlamıştır.
    GPT4_OT_Execute = None # Testlerin atlanması için bir işaretçi


class TestUndoRedo(unittest.TestCase):

    def setUp(self):
        """Her testten önce çalışır."""
        if GPT4_OT_Execute is None:
            self.skipTest("GPT4_OT_Execute operatörü yüklenemediği için testler atlandı.")

        bpy.ops.object.select_all(action='SELECT')
        bpy.ops.object.delete()
        for obj in bpy.data.objects:
            bpy.data.objects.remove(obj, do_unlink=True)
        for mesh in bpy.data.meshes:
            if not mesh.users:
                bpy.data.meshes.remove(mesh)
        for material in bpy.data.materials:
            if not material.users:
                bpy.data.materials.remove(material)
        for collection in bpy.data.collections:
            if not collection.users:
                bpy.data.collections.remove(collection)

        # Eklenti adını dinamik olarak al
        self.addon_module_name = os.path.basename(addon_root)
        prefs = bpy.context.preferences.addons[self.addon_module_name].preferences
        
        if not prefs.providers:
            provider = prefs.providers.add()
            provider.name = "TestOpenAI_UndoRedo"
            provider.type = 'openai'
            prefs.active_provider_index = 0
        
        active_provider = prefs.providers[prefs.active_provider_index]
        
        # API anahtarını ortam değişkeninden veya manuel olarak ayarla
        # ÖNEMLİ: Testlerde gerçek API anahtarı kullanmaktan kaçının veya çok dikkatli olun.
        # Güvenlik nedeniyle, bu kısmı CI/CD ortamlarında ortam değişkenleriyle yönetmek en iyisidir.
        # Lokal testler için geçici olarak bir anahtar girebilirsiniz ama bunu commit etmeyin.
        test_api_key = os.getenv("BLENDERGPT_TEST_API_KEY") # Ortam değişkeni adı
        if test_api_key:
            active_provider.api_key = test_api_key
        elif not active_provider.api_key: # Eğer tercihlerde de yoksa
             # Sahte bir anahtar ile devam etmeyi deneyebiliriz, ancak API çağrısı başarısız olacaktır.
             # Bu durumda, testler API çağrısı yapmayan kısımları test edebilir.
             # Ya da testi atlayabiliriz.
            print("UYARI: BLENDERGPT_TEST_API_KEY ortam değişkeni ayarlanmamış ve tercihlerde API anahtarı yok.")
            print("API çağrısı gerektiren testler başarısız olabilir veya atlanabilir.")
            # self.skipTest("API anahtarı testler için yapılandırılmamış.")
            active_provider.api_key = "DUMMY_API_KEY_FOR_TESTING" # API çağrısını tetiklemek için

        # Model ve diğer ayarlar
        if hasattr(bpy.context.scene, "gpt4_model"):
            bpy.context.scene.gpt4_model = "gpt-3.5-turbo" # Daha hızlı ve ucuz model testler için

        bpy.context.scene.gpt4_chat_input = ""


    def tearDown(self):
        """Her testten sonra çalışır."""
        bpy.context.scene.gpt4_chat_input = ""
        if hasattr(bpy.context.scene, "gpt4_chat_history"):
             bpy.context.scene.gpt4_chat_history.clear()
        
        # Test sırasında eklenen sağlayıcıyı temizleyebiliriz
        prefs = bpy.context.preferences.addons[self.addon_module_name].preferences
        # Basitlik adına, eğer sadece bizim eklediğimiz varsa onu silelim.
        # Daha karmaşık senaryolarda, setUp'ta eklenen sağlayıcının index'ini saklayıp onu silmek daha iyi olur.
        # for i, p in enumerate(prefs.providers):
        #     if p.name == "TestOpenAI_UndoRedo":
        #         prefs.providers.remove(i)
        #         if prefs.active_provider_index >= i and prefs.active_provider_index > 0:
        #             prefs.active_provider_index -=1
        #         elif len(prefs.providers) == 0:
        #             prefs.active_provider_index = 0
        #         break


    def _run_gpt_execute(self, command):
        """GPT4_OT_Execute operatörünü verilen komutla çalıştırır."""
        bpy.context.scene.gpt4_chat_input = command
        
        # Operatörün çağrılması. 'INVOKE_DEFAULT' yerine 'EXEC_DEFAULT' daha uygun olabilir
        # eğer operatör doğrudan çalıştırılıyorsa ve bir UI etkileşimi beklemiyorsa.
        # Ancak, 'INVOKE_DEFAULT' genellikle daha güvenlidir.
        # Operatörün bl_idname'sini kullanarak çağıralım.
        operator_idname = GPT4_OT_Execute.bl_idname
        bpy.ops.gpt4.send_message() # bl_idname'i doğrudan kullanmak yerine, operatörün çağrı şeklini kullanalım.
                                     # Eğer operatör adı farklıysa (örn: gpt4.execute_prompt), onu kullanın.
                                     # __init__.py'de GPT4_OT_Execute.bl_idname = "gpt4.send_message" olarak tanımlı.

        # API çağrısı ve kod yürütme zaman alabilir.
        # Blender'ın olay döngüsünün işlemesi için birkaç adım bekleyelim.
        # Bu, ideal bir çözüm değildir. Mümkünse, operatörün tamamlandığını
        # belirten bir geri bildirim mekanizması (örneğin bir özellik değişikliği) beklenmelidir.
        # Şimdilik, basit bir gecikme kullanalım.
        # Testlerin hızını etkileyebilir.
        # bpy.app.timers.register(lambda: None, first_interval=0.5) # 0.5 saniye bekleme (çok güvenilir değil)
        # Daha iyi bir yaklaşım, işlemin bittiğini gösteren bir durumu periyodik olarak kontrol etmektir.
        # Örneğin, chat history'e bir asistan mesajının eklenmesi.
        
        # Şimdilik, redraw_timer ile birkaç güncelleme döngüsü yapalım.
        # Bu, özellikle UI güncellemeleri ve kısa operasyonlar için işe yarayabilir.
        for _ in range(10): # Döngü sayısını artırabilir veya azaltabilirsiniz.
            bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)
            # bpy.context.view_layer.update() # Sahneyi güncellemeye zorla


    def test_create_cube_undo_redo(self):
        """Basit bir küp oluşturma, geri alma ve yineleme işlemini test eder."""
        initial_object_count = len(bpy.data.objects)
        initial_mesh_count = len(bpy.data.meshes)

        # 1. GPT ile bir küp oluştur
        # API anahtarının dummy olup olmadığını kontrol et, eğer öyleyse ve API çağrısı gerekiyorsa testi atla
        prefs = bpy.context.preferences.addons[self.addon_module_name].preferences
        active_provider = prefs.providers[prefs.active_provider_index]
        if active_provider.api_key == "DUMMY_API_KEY_FOR_TESTING":
            self.skipTest("Bu test için geçerli bir API anahtarı gerekiyor (DUMMY_API_KEY_FOR_TESTING bulundu).")

        self._run_gpt_execute("create a default cube") # "a default cube" daha spesifik olabilir

        # Operasyonun tamamlanması için biraz daha bekleyelim.
        # Bu kısım, API yanıt süresine bağlı olarak ayarlanmalıdır.
        # Gerçek bir uygulamada, burada daha sağlam bir bekleme mekanizması gerekir.
        # Örneğin, chat history'de asistan yanıtını beklemek.
        # Şimdilik, testin çalışması için yeterli sayıda redraw döngüsü varsayıyoruz.
        # Bu süre, internet bağlantısı ve API hızı gibi faktörlere bağlı olarak değişebilir.
        # Lokal testlerde bu süre daha kısa olabilir.
        # CI ortamlarında daha uzun bir süre gerekebilir.
        # Testin flakey (kararsız) olmaması için bu önemlidir.
        
        # Bekleme süresini artırabiliriz veya chat history'yi kontrol edebiliriz.
        # Örneğin, son mesajın asistan mesajı olup olmadığını kontrol et.
        max_wait_cycles = 60 # Yaklaşık 1 saniye (60Hz'de) veya daha fazla
        assistant_responded = False
        for i in range(max_wait_cycles):
            bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)
            if bpy.context.scene.gpt4_chat_history and bpy.context.scene.gpt4_chat_history[-1].type == 'assistant':
                assistant_responded = True
                break
            if i % 10 == 0: # Her 10 döngüde bir konsola ilerleme yazdır
                print(f"Waiting for assistant response... cycle {i+1}/{max_wait_cycles}")

        self.assertTrue(assistant_responded, "Asistan belirli bir süre içinde yanıt vermedi.")
        
        # Küpün oluştuğunu doğrula
        self.assertEqual(len(bpy.data.objects), initial_object_count + 1, f"Küp oluşturulamadı. Obje sayısı: {len(bpy.data.objects)}, beklenen: {initial_object_count + 1}")
        self.assertEqual(len(bpy.data.meshes), initial_mesh_count + 1, "Küp için mesh oluşturulamadı.")
        
        created_object = bpy.data.objects[-1] # Son eklenen obje genellikle yeni oluşturulandır
        self.assertIsNotNone(created_object, "Oluşturulan obje sahnede bulunamadı.")
        # self.assertTrue(created_object.name.startswith("Cube"), "Oluşturulan obje bir küp değil gibi görünüyor.")

        created_object_name = created_object.name # Geri alma/yineleme sonrası kontrol için adı sakla

        # 2. Geri Al (Undo)
        bpy.ops.ed.undo()
        for _ in range(10): bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

        # Küpün kaybolduğunu doğrula
        self.assertEqual(len(bpy.data.objects), initial_object_count, "Geri alma işlemi objeyi silmedi.")
        self.assertEqual(len(bpy.data.meshes), initial_mesh_count, "Geri alma işlemi mesh'i silmedi.")
        self.assertIsNone(bpy.data.objects.get(created_object_name), f"{created_object_name} adlı obje geri alındıktan sonra hala sahnede.")

        # 3. Yinele (Redo)
        bpy.ops.ed.redo()
        for _ in range(10): bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

        # Küpün tekrar göründüğünü doğrula
        self.assertEqual(len(bpy.data.objects), initial_object_count + 1, "Yineleme işlemi objeyi geri getirmedi.")
        self.assertEqual(len(bpy.data.meshes), initial_mesh_count + 1, "Yineleme işlemi mesh'i geri getirmedi.")
        restored_object = bpy.data.objects.get(created_object_name)
        self.assertIsNotNone(restored_object, f"{created_object_name} adlı obje yinelendikten sonra sahnede bulunamadı.")
        self.assertEqual(restored_object.name, created_object_name, "Yinelenen objenin adı orijinaliyle eşleşmiyor.")

# Testleri Blender'ın metin editöründen veya komut satırından çalıştırmak için:
# Metin Editörü: Bu script'i açın ve "Run Script" butonuna basın.
# Komut Satırı: blender --background --python tests/test_undo_redo.py
# (Blender'ın PATH'de olması ve eklentinin Blender tarafından bulunabilir olması gerekir)

def register_tests():
    # Bu fonksiyon, testleri Blender'ın test sistemiyle entegre etmek için kullanılabilir
    # Ancak şimdilik doğrudan unittest ile çalıştırıyoruz.
    pass

def unregister_tests():
    pass

if __name__ == "__main__":
    # Script doğrudan çalıştırıldığında testleri yürüt
    # Blender'ın test runner'ını kullanmak yerine standart unittest runner'ını kullanıyoruz.
    # Bu, Blender'ın UI'si olmadan da çalıştırılabilmesini sağlar (örn: --background modu).
    print("BlenderGPT Undo/Redo Testleri Başlatılıyor...")
    
    # Testleri çalıştırmadan önce eklentinin kayıtlı olduğundan emin olalım.
    # Bu, __init__.py dosyasındaki register() fonksiyonunu çağırmayı gerektirebilir,
    # eğer testler eklenti yüklenmeden önce çalıştırılıyorsa.
    # Ancak, eklentinin zaten etkin olduğunu varsayıyoruz (yukarıdaki enable bloğu ile).

    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestUndoRedo))
    runner = unittest.TextTestRunner(verbosity=2) # verbosity=2 daha fazla detay verir
    result = runner.run(suite)

    # Test sonuçlarına göre çıkış kodu ayarla (CI için faydalı)
    if not result.wasSuccessful():
        # sys.exit(1) # Hata durumunda çıkış yap (Blender'ı kapatabilir)
        print("Bazı testler başarısız oldu.")
    else:
        print("Tüm testler başarıyla tamamlandı.")

    # Blender'ı açık tutmak veya hemen kapatmak isteyip istemediğinize bağlı olarak
    # sys.exit(0) veya bpy.ops.wm.quit_blender() çağrılabilir.
    # --background modunda çalıştırılıyorsa, script bittiğinde Blender otomatik olarak kapanır.